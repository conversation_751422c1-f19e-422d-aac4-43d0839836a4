import React, { useState } from 'react';
import { useRestaurantReportForm } from '../hooks';
import type { Field } from '../types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Progress } from '@/shared/components/ui/progress';
import { Separator } from '@/shared/components/ui/separator';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import {
  Building2,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  AlertCircle,
  Eye,
  Settings,
  ChevronDown,
  ChevronUp,
  CalendarIcon,
} from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { format, isFuture } from 'date-fns';
import { Calendar } from '@/shared/components/ui/calendar';
import { cn } from '@/lib/utils';

/**
 * Simplified Restaurant Report Form Component
 *
 * A clean, modern interface for daily restaurant reporting with improved UX,
 * reduced visual clutter, and streamlined workflow following shadcn/ui design principles.
 */
const SalesEntryPage: React.FC = () => {
  // Placeholder functions for now, will need to be properly implemented or imported later
  const formatTime = (time: string) => time;
  const formatDateToDDMMYYYY = (date: Date | undefined) => {
    return date ? format(date, 'dd/MM/yyyy') : '';
  };

  const [reportDate, setReportDate] = useState<Date | undefined>(new Date());
  const [reportDateError, setReportDateError] = useState<string | null>(null);

  const validateReportDate = (date: Date | undefined) => {
    if (!date) {
      setReportDateError('Report date is required.');
      return false;
    }
    if (isFuture(date)) {
      setReportDateError('Future dates are not allowed for reports.');
      return false;
    }
    setReportDateError(null);
    return true;
  };

  const handleReportDateChange = (date: Date | undefined) => {
    setReportDate(date);
    validateReportDate(date);
  };

  const LoadingPage = () => <div>Loading...</div>; // Keep for now, but it might be removed if a global loading component is used.

  const [showManagementNote, setShowManagementNote] = useState(false);

  const [overrideDiscrepancy, setOverrideDiscrepancy] = useState(false);
  const {
    formData,
    currentStep,
    completedSections,
    errors,
    errorSections,
    handleChange,
    handleNumberBlur,
    handleSubmit,
    handleNext,
    handlePrevious,
    goToSection,
    sections,
    canProceed,
    isSubmitting,
    submitError,
    restaurantName,
    isLoading, // This isLoading comes from useRestaurantReportForm
  } = useRestaurantReportForm();

  // Skip the first section (Restaurant Details) since those are pre-filled values
  const activeSections = sections.slice(1);
  const adjustedCurrentStep = Math.max(0, currentStep - 1);
  const adjustedCurrentSection = activeSections[adjustedCurrentStep];
  const adjustedIsReviewStep = currentStep === sections.length;
  const adjustedIsLastFormStep = adjustedCurrentStep === activeSections.length - 1;

  // Remove empty useEffect - hooks compliance fix

  if (isLoading) {
    return <LoadingPage />;
  }

  const totalSteps = activeSections.length + 1;
  const progressPercentage = ((adjustedCurrentStep + 1) / totalSteps) * 100;

  const renderFormField = (field: Field) => {
    const isReadOnly = field.isCalculated || field.readOnly;
    const fieldError = errors[field.name];
    const value = formData[field.name as keyof typeof formData];

    return (
      <div key={field.name} className="space-y-2">
        <Label htmlFor={field.name} className="text-sm font-medium text-slate-700">
          {field.label}
          {field.optional && <span className="ml-1 text-slate-500">(optional)</span>}
        </Label>

        <div className="relative">
          {field.prefix && (
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-sm text-slate-500">
              {field.prefix}
            </span>
          )}

          <Input
            id={field.name}
            name={field.name}
            type={field.type}
            inputMode={field.type === 'number' ? 'decimal' : undefined}
            value={value}
            onChange={handleChange}
            onBlur={field.type === 'number' ? handleNumberBlur : undefined}
            readOnly={isReadOnly}
            placeholder={field.placeholder || (field.type === 'number' ? '0.00' : '')}
            className={`h-10 ${field.prefix ? 'pl-9' : ''} border-slate-200 focus:border-slate-400 focus:ring-slate-400 ${
              fieldError ? 'border-red-300 focus:border-red-400 focus:ring-red-400' : ''
            } ${isReadOnly ? 'cursor-not-allowed bg-slate-50 text-slate-600' : ''}`}
            autoComplete={field.autoComplete || 'off'}
            min={field.type === 'number' ? '0' : undefined}
            step={field.type === 'number' ? '0.01' : undefined}
          />
        </div>

        {field.description && !fieldError && (
          <p className="text-xs text-slate-500">{field.description}</p>
        )}

        {fieldError && (
          <p className="flex items-center gap-1 text-xs text-red-600">
            <AlertCircle className="h-3.5 w-3.5" />
            {fieldError}
          </p>
        )}
      </div>
    );
  };

  // Helper function to calculate totals for display
  const calculateTotals = () => {
    const parseNum = (val: number | '') => {
      if (val === '' || typeof val !== 'number' || isNaN(val)) return 0;
      return val;
    };

    const totalPayments =
      parseNum(formData.cashPayment) +
      parseNum(formData.visaPayment) +
      parseNum(formData.onlineTransactionPayment) +
      parseNum(formData.qrPayment);

    const totalSales = parseNum(formData.totalSales);
    const difference = totalPayments - totalSales;

    return { totalPayments, totalSales, difference };
  };

  const renderPaymentSummaryCard = () => {
    const { totalPayments, totalSales, difference } = calculateTotals();
    const hasDiscrepancy = Math.abs(difference) > 0.01;

    // Only show if both totals are greater than 0
    if (totalPayments === 0 && totalSales === 0) {
      return null;
    }

    return (
      <Card
        className={`mb-4 border-0 shadow-sm ${hasDiscrepancy ? 'bg-red-50/80' : 'bg-emerald-50/80'}`}
      >
        <CardHeader className="pb-3">
          <CardTitle
            className={`flex items-center gap-2 text-base ${hasDiscrepancy ? 'text-red-700' : 'text-emerald-700'}`}
          >
            {hasDiscrepancy ? (
              <AlertCircle className="h-4 w-4" />
            ) : (
              <CheckCircle2 className="h-4 w-4" />
            )}
            Payment Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
            <div className="rounded-lg border-0 bg-white/60 p-3 text-center">
              <div className="text-xs text-slate-600">Total Sales</div>
              <div className="text-lg font-semibold text-emerald-700">
                RM {totalSales.toFixed(2)}
              </div>
            </div>
            <div className="rounded-lg border-0 bg-white/60 p-3 text-center">
              <div className="text-xs text-slate-600">Total Payments</div>
              <div className="text-lg font-semibold text-slate-700">
                RM {totalPayments.toFixed(2)}
              </div>
            </div>
            <div className="rounded-lg border-0 bg-white/60 p-3 text-center">
              <div className="text-xs text-slate-600">Difference</div>
              <div
                className={`text-lg font-semibold ${hasDiscrepancy ? 'text-red-700' : 'text-emerald-700'}`}
              >
                {difference >= 0 ? '+' : ''}RM {difference.toFixed(2)}
              </div>
            </div>
          </div>
          {hasDiscrepancy && (
            <Alert variant="destructive" className="mt-3 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-700">
                <strong>Payment Mismatch:</strong> Total payments must equal total sales.
                {!overrideDiscrepancy && (
                  <Button
                    variant="link"
                    className="ml-1 h-auto p-0 text-red-700 underline"
                    onClick={() => setOverrideDiscrepancy(true)}
                  >
                    Proceed with discrepancy?
                  </Button>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderReviewSection = () => {
    const formatValue = (key: string, value: any) => {
      const field = sections.flatMap(s => s.fields).find(f => f.name === key);

      if (value === '' || value === null) {
        return <span className="text-muted-foreground">Not provided</span>;
      }

      if (field?.prefix) {
        return `${field.prefix} ${typeof value === 'number' ? value.toFixed(2) : value}`;
      }

      if (field?.type === 'time') {
        return value;
      }

      if (field?.type === 'date') {
        let dateToFormat: Date | null = null;
        if (value instanceof Date) {
          dateToFormat = value;
        } else if (typeof value === 'string') {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            dateToFormat = parsedDate;
          }
        }
        if (dateToFormat) {
          return formatDateToDDMMYYYY(dateToFormat);
        }
      }

      return value;
    };

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="bg-primary text-primary-foreground rounded-lg p-2">
              <Eye className="h-5 w-5" />
            </div>
            <div>
              <CardTitle>Review Your Report</CardTitle>
              <CardDescription>
                Please review all information carefully before final submission.
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Payment vs Sales Summary */}
          {renderPaymentSummaryCard()}

          {/* Restaurant Details Summary */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <div className="rounded-md bg-sky-500 p-1.5">
                <Building2 className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-sm font-semibold">Restaurant Details</h3>
            </div>

            <div className="grid grid-cols-1 gap-4 pl-7 md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-1">
                <p className="text-muted-foreground text-xs">Restaurant Name</p>
                <p className="text-sm font-medium">{restaurantName}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground text-xs">Management Note</p>
                <p className="text-sm font-medium">
                  {formData.managementNote || (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground text-xs">Opening Hours</p>
                <p className="text-sm font-medium">{formatTime(formData.openTime)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground text-xs">Closing Hours</p>
                <p className="text-sm font-medium">{formatTime(formData.closeTime)}</p>
              </div>
            </div>

            <Separator />
          </div>

          {activeSections.map((section, index) => (
            <div key={section.title} className="space-y-3">
              <div className="flex items-center gap-2">
                <div className={`rounded-md p-1.5 ${section.color}`}>
                  <section.icon className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-sm font-semibold">{section.title}</h3>
              </div>

              <div className="grid grid-cols-1 gap-4 pl-7 md:grid-cols-2 lg:grid-cols-3">
                {section.fields.map(field => (
                  <div key={field.name} className="space-y-1">
                    <p className="text-muted-foreground text-xs">{field.label}</p>
                    <p className="text-sm font-medium">
                      {formatValue(field.name, formData[field.name as keyof typeof formData])}
                    </p>
                  </div>
                ))}
              </div>

              {index < activeSections.length - 1 && <Separator />}
            </div>
          ))}

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Note:</strong> Once submitted, this report will be saved to the system. Please
              ensure all information is accurate before proceeding.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header Section - Following standard page pattern */}
      <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-3">
          <Building2 className="text-primary h-8 w-8" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Daily Sales Report</h1>
            <p className="text-muted-foreground">
              {restaurantName} • Report for{' '}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'h-8 w-[140px] justify-start text-left font-normal',
                      !reportDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {reportDate ? formatDateToDDMMYYYY(reportDate) : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={reportDate}
                    onSelect={handleReportDateChange}
                    disabled={date => isFuture(date)}
                  />
                </PopoverContent>
              </Popover>
              {reportDateError && (
                <span className="ml-2 flex items-center gap-1 text-xs text-red-600">
                  <AlertCircle className="h-3 w-3" />
                  {reportDateError}
                </span>
              )}
            </p>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="text-xs sm:text-sm">
            Step {adjustedCurrentStep + 1} of {totalSteps}
          </Badge>
          <div className="w-20 sm:w-24">
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-4 lg:gap-6">
        {/* Sidebar Navigation */}
        <nav className="lg:col-span-1" aria-label="Form sections navigation">
          <Card className="sticky top-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium">Sections</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {activeSections.map((section, index) => {
                const Icon = section.icon;
                const originalIndex = index + 1;
                const isCompleted = completedSections.has(originalIndex);
                const isCurrent = index === adjustedCurrentStep;
                const hasError = errorSections.has(originalIndex);

                return (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => goToSection(originalIndex)}
                    className={`h-auto w-full justify-start rounded-lg p-3 transition-all ${
                      isCurrent
                        ? 'bg-slate-100 text-slate-900 shadow-sm'
                        : hasError
                          ? 'text-red-600 hover:bg-red-50'
                          : isCompleted
                            ? 'text-emerald-600 hover:bg-emerald-50'
                            : 'text-slate-600 hover:bg-slate-50'
                    }`}
                    aria-label={`${section.title} section${isCurrent ? ' (current)' : ''}${hasError ? ' (has errors)' : ''}${isCompleted ? ' (completed)' : ''}`}
                    aria-current={isCurrent ? 'step' : undefined}
                  >
                    <div className="flex w-full items-center gap-3">
                      <div
                        className={`rounded-lg p-1.5 ${
                          isCurrent
                            ? 'bg-slate-200'
                            : hasError
                              ? 'bg-red-100'
                              : isCompleted
                                ? 'bg-emerald-100'
                                : 'bg-slate-100'
                        }`}
                      >
                        <Icon
                          className={`h-3.5 w-3.5 ${
                            isCurrent
                              ? 'text-slate-700'
                              : hasError
                                ? 'text-red-600'
                                : isCompleted
                                  ? 'text-emerald-600'
                                  : 'text-slate-500'
                          }`}
                        />
                      </div>

                      <div className="flex-1 text-left">
                        <p className="truncate text-sm font-medium">{section.title}</p>
                        {hasError && !isCurrent && (
                          <p className="text-xs text-red-500">Needs attention</p>
                        )}
                        {isCompleted && !hasError && !isCurrent && (
                          <p className="text-xs text-emerald-500">Completed</p>
                        )}
                      </div>

                      {isCompleted && !hasError && !isCurrent && (
                        <CheckCircle2 className="h-3.5 w-3.5 text-emerald-500" />
                      )}
                      {hasError && !isCurrent && (
                        <AlertCircle className="h-3.5 w-3.5 text-red-500" />
                      )}
                    </div>
                  </Button>
                );
              })}

              {/* Review Step */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => goToSection(sections.length)}
                className={`h-auto w-full justify-start rounded-lg p-3 transition-all ${
                  adjustedIsReviewStep
                    ? 'bg-slate-100 text-slate-900 shadow-sm'
                    : 'text-slate-600 hover:bg-slate-50'
                }`}
                disabled={!adjustedIsLastFormStep && adjustedCurrentStep < activeSections.length}
                aria-label={`Review and submit section${adjustedIsReviewStep ? ' (current)' : ''}`}
                aria-current={adjustedIsReviewStep ? 'step' : undefined}
              >
                <div className="flex w-full items-center gap-3">
                  <div
                    className={`rounded-lg p-1.5 ${
                      adjustedIsReviewStep ? 'bg-slate-200' : 'bg-slate-100'
                    }`}
                  >
                    <Eye
                      className={`h-3.5 w-3.5 ${
                        adjustedIsReviewStep ? 'text-slate-700' : 'text-slate-500'
                      }`}
                    />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-sm font-medium">Review & Submit</p>
                  </div>
                </div>
              </Button>
            </CardContent>
          </Card>
        </nav>

        {/* Form Content */}
        <div className="lg:col-span-3">
          <form
            onSubmit={handleSubmit}
            className="space-y-6"
            role="form"
            aria-label="Daily sales report form"
          >
            {adjustedIsReviewStep ? (
              renderReviewSection()
            ) : (
              <>
                {/* Collapsible Management Note */}
                <Card>
                  <CardHeader className="pb-3">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={() => setShowManagementNote(!showManagementNote)}
                      className="h-auto w-full justify-between p-0 text-left hover:bg-transparent"
                      aria-expanded={showManagementNote}
                      aria-controls="management-note-content"
                    >
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4 text-slate-500" />
                        <CardTitle className="text-base font-medium text-slate-700">
                          Management Note (Optional)
                        </CardTitle>
                      </div>
                      {showManagementNote ? (
                        <ChevronUp className="h-4 w-4 text-slate-500" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-slate-500" />
                      )}
                    </Button>
                  </CardHeader>

                  {showManagementNote && (
                    <CardContent className="pt-0" id="management-note-content">
                      <div className="space-y-3">
                        <Label
                          htmlFor="managementNote"
                          className="text-sm font-medium text-slate-600"
                        >
                          Manager / Shift Notes
                        </Label>
                        <Input
                          id="managementNote"
                          name="managementNote"
                          type="text"
                          value={formData.managementNote}
                          onChange={handleChange}
                          placeholder="e.g., John Doe (Shift Manager)"
                          className="h-10 border-slate-200 focus:border-slate-400 focus:ring-slate-400"
                          autoComplete="off"
                          aria-describedby="management-note-description"
                        />
                        <p id="management-note-description" className="sr-only">
                          Optional field for recording the shift manager's name or important notes
                          about the shift
                        </p>
                      </div>
                    </CardContent>
                  )}
                </Card>

                {/* Current Section */}
                {adjustedCurrentSection && (
                  <>
                    <Card>
                      <CardHeader className="pb-4">
                        <div className="flex items-center gap-3">
                          <div className="rounded-xl bg-slate-100 p-2.5">
                            <adjustedCurrentSection.icon className="h-5 w-5 text-slate-600" />
                          </div>
                          <div>
                            <CardTitle className="text-lg font-semibold text-slate-800">
                              {adjustedCurrentSection.title}
                            </CardTitle>
                            <CardDescription className="text-slate-600">
                              {adjustedCurrentSection.description}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                          {adjustedCurrentSection.fields.map(renderFormField)}
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}
              </>
            )}

            {/* Navigation */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    size="default"
                    onClick={handlePrevious}
                    disabled={adjustedCurrentStep === 0 || isSubmitting}
                    className="flex items-center justify-center gap-2 border-slate-200 px-4 text-slate-600 hover:bg-slate-50 sm:justify-start"
                    aria-label={
                      adjustedIsReviewStep ? 'Go back to edit form' : 'Go to previous section'
                    }
                  >
                    <ChevronLeft className="h-4 w-4" aria-hidden="true" />
                    <span className="sm:inline">
                      {adjustedIsReviewStep ? 'Back to Edit' : 'Previous'}
                    </span>
                  </Button>

                  {adjustedIsReviewStep ? (
                    <Button
                      type="submit"
                      size="default"
                      disabled={
                        isSubmitting ||
                        (Math.abs(calculateTotals().difference) > 0.01 && !overrideDiscrepancy)
                      }
                      className="flex items-center justify-center gap-2 bg-emerald-600 px-6 text-white hover:bg-emerald-700"
                      aria-label="Submit the completed sales report"
                    >
                      {isSubmitting ? 'Submitting...' : 'Submit Report'}
                      {!isSubmitting && <CheckCircle2 className="h-4 w-4" aria-hidden="true" />}
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      size="default"
                      onClick={handleNext}
                      disabled={!canProceed}
                      className="flex items-center justify-center gap-2 bg-slate-800 px-4 text-white hover:bg-slate-900"
                      aria-label={
                        adjustedIsLastFormStep ? 'Go to review section' : 'Continue to next section'
                      }
                    >
                      <span className="sm:inline">
                        {adjustedIsLastFormStep ? 'Review Report' : 'Continue'}
                      </span>
                      {adjustedIsLastFormStep ? (
                        <Eye className="h-4 w-4" aria-hidden="true" />
                      ) : (
                        <ChevronRight className="h-4 w-4" aria-hidden="true" />
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SalesEntryPage;
