import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';

type WhatsAppStatus = {
  isConnected: boolean;
  isConnecting: boolean;
  reconnectAttempts: number;
};

export const useWhatsAppStatus = () => {
  const [status, setStatus] = useState<WhatsAppStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  const fetchStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.get(API_ENDPOINTS.WHATSAPP.STATUS);
      setStatus(response.data);
    } catch (error) {
      console.error('Failed to fetch WhatsApp status:', error);
      setStatus(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, 5000);
    return () => clearInterval(interval);
  }, [fetchStatus]);

  const disconnect = useCallback(async () => {
    try {
      setIsDisconnecting(true);
      await apiClient.post(API_ENDPOINTS.WHATSAPP.DISCONNECT);
      await fetchStatus();
    } catch (error) {
      console.error('Failed to disconnect WhatsApp:', error);
    } finally {
      setIsDisconnecting(false);
    }
  }, [fetchStatus]);

  return { status, isLoading, isDisconnecting, disconnect, refetch: fetchStatus };
};
