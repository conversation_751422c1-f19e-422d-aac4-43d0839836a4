import { QRCodeDisplay, LoginInstructions } from '../components';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { MessageCircle } from 'lucide-react';
import { useWhatsAppStatus } from '../hooks/useWhatsAppStatus';
import { ActiveConnectionDisplay } from '../components/ActiveConnectionDisplay';

const WhatsAppLoginPage = () => {
  const { status, isLoading, isDisconnecting, disconnect } = useWhatsAppStatus();

  return (
    <div className="space-y-6 md:space-y-8">
      <div className="space-y-2 md:space-y-3">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">WhatsApp Login</h1>
        <p className="text-muted-foreground text-sm md:text-base">
          {status?.isConnected
            ? 'Your WhatsApp account is connected.'
            : 'Scan the QR code to connect your WhatsApp account.'}
        </p>
      </div>
      <Card className="border-muted/40">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MessageCircle className="text-primary h-5 w-5" />
            <span>
              {isLoading
                ? 'Checking Status...'
                : status?.isConnected
                  ? 'WhatsApp Connected'
                  : 'Scan QR Code'}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center">
          {isLoading ? (
            <div className="text-center">
              <p>Loading WhatsApp status...</p>
            </div>
          ) : status?.isConnected ? (
            <ActiveConnectionDisplay onDisconnect={disconnect} isDisconnecting={isDisconnecting} />
          ) : (
            <>
              <QRCodeDisplay />
              <LoginInstructions />
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WhatsAppLoginPage;
